# 🔒 Security Scanner Web Interface

Una herramienta web interactiva avanzada para análisis de seguridad que integra múltiples herramientas de reconocimiento y análisis de vulnerabilidades con **salida en tiempo real de Nuclei** y **reportes HTML profesionales**.

## ✨ Características Principales

- **🎯 Interfaz web moderna** con Bootstrap 5 y diseño responsive
- **🔄 Análisis multi-herramienta** integrado con 5 herramientas de seguridad
- **⚡ Resultados en tiempo real** con actualizaciones automáticas cada segundo
- **🐛 Consola de Nuclei en vivo** con salida línea por línea
- **📊 Reportes HTML profesionales** con métricas avanzadas y score de riesgo
- **💾 Service Worker** para mejor experiencia offline
- **🔄 Persistencia de resultados** entre sesiones
- **📱 Diseño responsive** optimizado para móviles y tablets
- **🖨️ Reportes optimizados** para impresión y compartir

## 🛠️ Herramientas Integradas

| Herramienta | Función | Estado |
|-------------|---------|--------|
| **WHOIS** | Información de dominio y registro | ✅ Integrado |
| **DIG** | Consultas DNS (A, MX, NS) | ✅ Integrado |
| **theHarvester** | Recolección de información OSINT | ⚠️ Opcional |
| **AssetFinder** | Enumeración de subdominios | ✅ Integrado |
| **Nuclei** | Análisis de vulnerabilidades con **tiempo real** | ✅ Integrado |

## 🚀 Nuevas Características v2.0

### 🐛 **Nuclei en Tiempo Real**
- **Consola visual** tipo terminal con tema oscuro
- **Salida línea por línea** con timestamps
- **Clasificación automática** de líneas (vulnerabilidades, errores, info)
- **Estadísticas en vivo** (templates, requests, tiempo)
- **Controles interactivos** (pausar, limpiar, auto-scroll)

### 📊 **Reportes HTML Avanzados**
- **Resumen ejecutivo** con métricas clave
- **Score de riesgo** automático (0-100)
- **Timeline visual** del análisis
- **Recomendaciones inteligentes** priorizadas
- **Diseño profesional** con gradientes y animaciones
- **Optimizado para impresión** con botón flotante

### 🎨 **Interfaz Mejorada**
- **Cards modernas** con efectos hover
- **Indicadores de estado** en tiempo real
- **Animaciones suaves** y transiciones
- **Colores por severidad** de vulnerabilidades
- **Responsive design** para todos los dispositivos

## 📦 Instalación

### 🚀 Instalación Automática (Recomendada)

```bash
git clone https://github.com/stoja88/security_scanner_web.git
cd security_scanner_web
chmod +x start.sh
./start.sh
```

El script `start.sh` se encarga automáticamente de:
- ✅ Crear un entorno virtual de Python
- ✅ Instalar dependencias de Python
- ✅ Verificar e instalar herramientas de seguridad
- ✅ Diagnosticar el sistema
- ✅ Iniciar la aplicación web

### 🔧 Instalación Manual

```bash
# Clonar el repositorio
git clone https://github.com/stoja88/security_scanner_web.git
cd security_scanner_web

# Crear entorno virtual
python3 -m venv venv
source venv/bin/activate  # En Windows: venv\Scripts\activate

# Instalar dependencias
pip install -r requirements.txt

# Ejecutar la aplicación
python app.py
```

### 📋 Requisitos del Sistema

- **Python 3.8+**
- **pip** (gestor de paquetes Python)
- **Go** (para AssetFinder y Nuclei)
- **dig** y **whois** (usualmente preinstalados en Linux/macOS)

## 🎯 Uso

### 1. **Iniciar la Aplicación**
```bash
./start.sh
```
La aplicación estará disponible en: `http://localhost:3333`

### 2. **Ejecutar Análisis**
1. 🌐 Accede a `http://localhost:3333` en tu navegador
2. 📝 Ingresa el dominio o IP que deseas analizar
3. 🚀 Haz clic en "Iniciar Análisis"
4. 👀 Observa los resultados en tiempo real
5. 🐛 Ve la consola de Nuclei ejecutándose en vivo
6. 📊 Descarga el reporte HTML profesional cuando termine

### 3. **Características del Análisis**
- **⏱️ Tiempo real**: Ve el progreso de cada herramienta
- **🐛 Consola Nuclei**: Salida línea por línea con colores
- **📊 Métricas**: Templates cargados, requests enviados, vulnerabilidades
- **🎯 Estados**: Completado, error, timeout con indicadores visuales

## 📁 Estructura del Proyecto

```
security_scanner_web/
├── 📄 app.py                          # Aplicación principal Flask
├── 📄 requirements.txt                # Dependencias Python
├── 🚀 start.sh                        # Script de inicio automático
├── 📁 static/                         # Archivos estáticos
│   ├── 🎨 css/
│   │   └── custom.css                 # Estilos personalizados
│   ├── ⚡ js/
│   │   └── app.js                     # JavaScript principal
│   └── 📦 vendor/                     # Librerías externas
├── 📁 templates/                      # Plantillas HTML
│   ├── index.html                     # Interfaz principal
│   ├── report.html                    # Reporte básico (legacy)
│   └── report_enhanced.html           # Reporte avanzado v2.0
├── 📁 reports/                        # Reportes generados
└── 📖 README.md                       # Este archivo
```

## ⚙️ Configuración

### 🌍 Variables de Entorno

| Variable | Descripción | Por Defecto |
|----------|-------------|-------------|
| `PORT` | Puerto del servidor | `3333` |
| `RAILWAY_ENVIRONMENT_NAME` | Detecta Railway automáticamente | `local` |

### 🔧 Personalización

Puedes modificar:
- **Concurrencia**: `MAX_CONCURRENT_SCANS` en `app.py`
- **Timeouts**: Valores individuales por herramienta
- **Estilos**: `static/css/custom.css`
- **Nuclei**: Configuración de templates y severidad

## 🔧 Características Técnicas

### 🏗️ Backend
- **Framework**: Flask con threading
- **Persistencia**: Archivos JSON
- **Concurrencia**: Análisis simultáneos limitados
- **Tiempo Real**: Polling cada segundo
- **Fallbacks**: Sistema robusto para Nuclei

### 🎨 Frontend
- **Framework**: Bootstrap 5
- **JavaScript**: Vanilla ES6+
- **Tiempo Real**: Fetch API con polling
- **Responsive**: Mobile-first design
- **PWA**: Service Worker integrado

### 🐛 Integración Nuclei
- **Captura**: Línea por línea en tiempo real
- **Clasificación**: Automática por tipo de salida
- **Estadísticas**: Templates, requests, vulnerabilidades
- **Fallbacks**: Configuración simplificada si falla

## 📈 Métricas y Reportes

### 📊 Score de Riesgo
- **Cálculo automático** basado en severidad de vulnerabilidades
- **Escala 0-100** con niveles de riesgo
- **Colores visuales** para fácil interpretación

### 📋 Recomendaciones
- **Priorizadas** por severidad (1-5)
- **Contextualizadas** según hallazgos
- **Accionables** con pasos específicos

### ⏰ Timeline
- **Eventos cronológicos** del análisis
- **Timestamps precisos** de cada herramienta
- **Estados visuales** con iconos y colores

## 🔄 API Endpoints

- `POST /scan` - Iniciar nuevo análisis
- `GET /status/{scan_id}` - Obtener estado del análisis
- `GET /report/{scan_id}` - Descargar reporte HTML mejorado
- `GET /live-output/{scan_id}` - Obtener salida en tiempo real de Nuclei
- `GET /nuclei-console/{scan_id}` - Endpoint específico para consola Nuclei

## 🤝 Contribuir

1. 🍴 Fork el proyecto
2. 🌿 Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. 💾 Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. 📤 Push a la rama (`git push origin feature/AmazingFeature`)
5. 🔄 Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.

## 🆘 Soporte

Si encuentras algún problema o tienes sugerencias:
- 🐛 Abre un **issue** en GitHub
- 💬 Describe el problema detalladamente
- 📋 Incluye logs y capturas si es posible

## ⚠️ Disclaimer

Esta herramienta está destinada únicamente para:
- ✅ **Sistemas propios** o con autorización explícita
- ✅ **Fines educativos** y de investigación
- ✅ **Auditorías de seguridad** autorizadas

❌ **El uso no autorizado puede violar leyes locales e internacionales.**

---

## 🎉 Changelog v2.0

### ✨ Nuevas Características
- 🐛 **Nuclei en tiempo real** con consola visual
- 📊 **Reportes HTML avanzados** con métricas profesionales
- 🎨 **Interfaz mejorada** con diseño moderno
- ⏰ **Timeline del análisis** con eventos cronológicos
- 💡 **Recomendaciones inteligentes** contextualizadas

### 🔧 Mejoras Técnicas
- ⚡ **Rendimiento optimizado** con polling eficiente
- 🛡️ **Manejo robusto de errores** y fallbacks
- 📱 **Responsive design** mejorado
- 🖨️ **Optimización para impresión** de reportes

### 🐛 Correcciones
- ✅ **Nuclei timeout** manejado correctamente
- ✅ **Compatibilidad** con versiones anteriores
- ✅ **Estabilidad** mejorada en análisis largos

**🚀 ¡Disfruta del Security Scanner Web Interface v2.0!**

