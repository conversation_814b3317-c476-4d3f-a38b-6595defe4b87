#!/usr/bin/env python3
"""
Script simple para reparar la configuración de Nuclei
"""

import subprocess
import time

def test_nuclei_simple():
    """Prueba simple de Nuclei"""
    print("🧪 Probando Nuclei con configuración simple...")
    
    # Comando ultra-simple
    cmd = [
        "nuclei", 
        "-target", "https://httpbin.org",
        "-tags", "tech",
        "-silent",
        "-timeout", "5",
        "-c", "1",
        "-disable-update-check",
        "-no-color"
    ]
    
    print(f"Comando: {' '.join(cmd)}")
    
    try:
        # Usar timeout del sistema
        result = subprocess.run(
            ["timeout", "15"] + cmd,
            capture_output=True,
            text=True,
            timeout=20
        )
        
        print(f"Return code: {result.returncode}")
        print(f"Stdout length: {len(result.stdout)}")
        print(f"Stderr length: {len(result.stderr)}")
        
        if result.returncode == 0:
            print("✅ Nuclei funcionó correctamente")
            if result.stdout:
                print(f"Output: {result.stdout[:200]}...")
        elif result.returncode == 124:
            print("⏰ Nuclei tuvo timeout (normal)")
        else:
            print(f"⚠️  Nuclei terminó con código {result.returncode}")
            if result.stderr:
                print(f"Error: {result.stderr[:200]}")
                
        return True
        
    except subprocess.TimeoutExpired:
        print("⏰ Timeout del script (normal)")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_nuclei_installation():
    """Verifica la instalación de Nuclei"""
    print("🔍 Verificando instalación de Nuclei...")
    
    try:
        # Verificar que esté en PATH
        result = subprocess.run(["which", "nuclei"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Nuclei encontrado en: {result.stdout.strip()}")
        else:
            print("❌ Nuclei no encontrado en PATH")
            return False
            
        # Verificar versión
        result = subprocess.run(["nuclei", "-version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Versión: {result.stdout.strip()}")
        else:
            print("⚠️  No se pudo obtener la versión")
            
        return True
        
    except Exception as e:
        print(f"❌ Error verificando Nuclei: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Diagnóstico simple de Nuclei")
    print("=" * 50)
    
    # Verificar instalación
    if not check_nuclei_installation():
        print("❌ Nuclei no está correctamente instalado")
        exit(1)
    
    print()
    
    # Probar funcionamiento
    if test_nuclei_simple():
        print("\n✅ Nuclei está funcionando. Las mejoras en app.py deberían resolver los timeouts.")
    else:
        print("\n❌ Hay problemas con Nuclei que requieren investigación adicional.")
    
    print("\n📋 Recomendaciones:")
    print("1. La aplicación web ahora tiene múltiples fallbacks para Nuclei")
    print("2. Si Nuclei tiene timeout, se usa configuración más ligera")
    print("3. Como último recurso, se hace verificación básica de seguridad")
    print("4. Reinicia la aplicación web para aplicar las mejoras")
