# Fly.io deployment configuration
app = "security-scanner-web"
primary_region = "ord"

[build]
  dockerfile = "deployments/fly/Dockerfile.fly"

[env]
  FLASK_ENV = "production"
  FLASK_DEBUG = "false"
  MAX_CONCURRENT_SCANS = "2"
  SCAN_TIMEOUT = "300"
  PYTHONUNBUFFERED = "1"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[http_service.checks]]
  grace_period = "10s"
  interval = "30s"
  method = "GET"
  timeout = "5s"
  path = "/"

[vm]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 1024

[[mounts]]
  source = "security_scanner_data"
  destination = "/app/reports"
