# AWS App Runner configuration
version: 1.0
runtime: docker
build:
  commands:
    build:
      - echo "Building Security Scanner Web Interface for AWS App Runner"
run:
  runtime-version: latest
  command: gunicorn --bind 0.0.0.0:8080 --workers 2 --timeout 120 app:app
  network:
    port: 8080
    env: PORT
  env:
    - name: FLASK_ENV
      value: production
    - name: FLASK_DEBUG
      value: "false"
    - name: MAX_CONCURRENT_SCANS
      value: "2"
    - name: SCAN_TIMEOUT
      value: "300"
    - name: PYTHONUNBUFFERED
      value: "1"
