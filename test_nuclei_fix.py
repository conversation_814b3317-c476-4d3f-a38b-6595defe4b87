#!/usr/bin/env python3
"""
Test script para verificar las mejoras de Nuclei
"""

import sys
import os
import subprocess
import time
import json

# Agregar el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import SecurityScanner

def test_nuclei_improvements():
    """Prueba las mejoras de Nuclei"""
    print("🧪 Probando mejoras de Nuclei...")
    print("=" * 60)
    
    # Crear scanner de prueba
    test_target = "httpbin.org"
    scan_id = "test-nuclei-fix"
    
    scanner = SecurityScanner(test_target, scan_id)
    
    print(f"📍 Target de prueba: {test_target}")
    print(f"🆔 Scan ID: {scan_id}")
    print()
    
    # Probar solo Nuclei
    print("🔍 Ejecutando análisis de Nuclei...")
    start_time = time.time()
    
    try:
        scanner.run_nuclei()
        end_time = time.time()
        
        print(f"⏱️  Tiempo total: {end_time - start_time:.2f} segundos")
        print()
        
        # Verificar resultados
        nuclei_result = scanner.results['tools_results'].get('nuclei', {})
        status = nuclei_result.get('status', 'unknown')
        
        print(f"📊 Estado de Nuclei: {status}")
        
        if status == 'completed':
            print("✅ Nuclei completado exitosamente")
            vulnerabilities = scanner.results.get('vulnerabilities', [])
            print(f"🔒 Vulnerabilidades encontradas: {len(vulnerabilities)}")
            
            if vulnerabilities:
                for vuln in vulnerabilities[:3]:  # Mostrar solo las primeras 3
                    print(f"   - {vuln.get('name', 'Unknown')} ({vuln.get('severity', 'unknown')})")
            
        elif status == 'timeout':
            print("⏰ Nuclei tuvo timeout pero se manejó correctamente")
            
        elif status == 'error':
            print("❌ Nuclei tuvo errores:")
            print(f"   Error: {nuclei_result.get('errors', 'Unknown')}")
            
        else:
            print(f"⚠️  Estado inesperado: {status}")
            
        # Mostrar output si existe
        output = nuclei_result.get('output', '')
        if output and len(output) > 0:
            print(f"📄 Output disponible: {len(output)} caracteres")
        
        return status == 'completed'
        
    except Exception as e:
        print(f"❌ Error durante la prueba: {e}")
        return False

def test_basic_check():
    """Prueba la verificación básica sin Nuclei"""
    print("\n🔧 Probando verificación básica...")
    print("=" * 60)
    
    scanner = SecurityScanner("httpbin.org", "test-basic")
    
    try:
        stdout, stderr, returncode = scanner.run_nuclei_basic_check("https://httpbin.org")
        
        print(f"📊 Return code: {returncode}")
        print(f"📄 Output length: {len(stdout) if stdout else 0}")
        
        if stdout:
            try:
                # Intentar parsear como JSON
                lines = stdout.strip().split('\n')
                results = [json.loads(line) for line in lines if line.strip()]
                print(f"✅ Verificación básica encontró {len(results)} resultados")
                
                for result in results:
                    name = result.get('info', {}).get('name', 'Unknown')
                    severity = result.get('info', {}).get('severity', 'unknown')
                    print(f"   - {name} ({severity})")
                    
                return True
                
            except json.JSONDecodeError:
                print(f"⚠️  Output no es JSON válido: {stdout[:100]}")
                return False
        else:
            print("ℹ️  Sin resultados de verificación básica")
            return True
            
    except Exception as e:
        print(f"❌ Error en verificación básica: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Iniciando pruebas de mejoras de Nuclei")
    print("=" * 60)
    
    # Verificar que Nuclei esté disponible
    try:
        result = subprocess.run(['which', 'nuclei'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Nuclei encontrado en: {result.stdout.strip()}")
        else:
            print("❌ Nuclei no encontrado en PATH")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error verificando Nuclei: {e}")
        sys.exit(1)
    
    print()
    
    # Ejecutar pruebas
    test1_passed = test_nuclei_improvements()
    test2_passed = test_basic_check()
    
    print("\n" + "=" * 60)
    print("📋 RESUMEN DE PRUEBAS")
    print("=" * 60)
    print(f"🧪 Prueba de Nuclei mejorado: {'✅ PASÓ' if test1_passed else '❌ FALLÓ'}")
    print(f"🔧 Prueba de verificación básica: {'✅ PASÓ' if test2_passed else '❌ FALLÓ'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ¡Todas las pruebas pasaron! Las mejoras de Nuclei están funcionando.")
    else:
        print("\n⚠️  Algunas pruebas fallaron. Revisar la configuración.")
    
    print("=" * 60)
