{"scan_id": "3bec6033-7518-436a-9f6a-b54d47568ebb", "target": "localhost.com", "status": "completed", "start_time": "2025-05-29T22:19:31.957310", "end_time": "2025-05-29T22:19:47.858576", "tools_results": {"whois": {"status": "completed", "progress": "Información de registro obtenida", "output": {"domain": "localhost.com", "registrar": "Example Registrar Inc.", "creation_date": "2020-01-15", "expiration_date": "2025-01-15", "name_servers": ["ns1.example.com", "ns2.example.com"], "status": "Active"}, "raw_output": "Domain: localhost.com\nRegistrar: Example Registrar Inc.\nCreated: 2020-01-15\nExpires: 2025-01-15"}, "dig": {"status": "completed", "progress": "Registros DNS obtenidos", "output": {"A": ["*************", "*************"], "MX": ["10 mail.example.com", "20 mail2.example.com"], "NS": ["ns1.example.com", "ns2.example.com"], "TXT": ["v=spf1 include:_spf.example.com ~all"]}}, "theharvester": {"status": "completed", "progress": "3 emails y 4 hosts encontrados", "output": {"emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "hosts": ["www.localhost.com", "mail.localhost.com", "ftp.localhost.com", "blog.localhost.com"]}}, "assetfinder": {"status": "completed", "progress": "5 subdominios encontrados", "output": ["www.localhost.com", "api.localhost.com", "cdn.localhost.com", "staging.localhost.com", "dev.localhost.com"]}, "nuclei": {"status": "completed", "progress": "2 vulnerabilidades encontradas", "output": [{"template-id": "ssl-tls-version", "info": {"name": "SSL/TLS Version Detection", "severity": "info"}, "matched-at": "https://localhost.com", "extracted-results": ["TLSv1.2", "TLSv1.3"]}, {"template-id": "http-missing-security-headers", "info": {"name": "Missing Security Headers", "severity": "low"}, "matched-at": "https://localhost.com", "extracted-results": ["X-Frame-Options", "X-Content-Type-Options"]}]}}, "summary": {"total_tools": 5, "successful_tools": 5, "failed_tools": 0, "vulnerabilities_found": 2, "subdomains_found": 5, "emails_found": 3}, "recommendations": ["Se encontraron vulnerabilidades. Revisar y corregir los problemas identificados.", "Se encontraron direcciones de email públicas. Considerar medidas anti-spam."]}