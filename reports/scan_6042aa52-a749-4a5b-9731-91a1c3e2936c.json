{"target": "karedesk.com", "scan_id": "6042aa52-a749-4a5b-9731-91a1c3e2936c", "start_time": "2025-05-30T08:09:49.220195", "end_time": "2025-05-30T08:10:28.798251", "status": "completed_with_errors", "progress": "Completado con errores - 3/5 herramientas exitosas", "tools_results": {"whois": {"status": "completed", "output": "Domain Name: KAREDESK.COM\n   Registry Domain ID: 2608226545_DOMAIN_COM-VRSN\n   Registrar WHOIS Server: whois.hostinger.com\n   Registrar URL: http://www.hostinger.com\n   Updated Date: 2025-05-26T05:08:27Z\n   Creation Date: 2021-04-28T05:24:32Z\n   Registry Expiry Date: 2027-04-28T05:24:32Z\n   Registrar: HOSTINGER operations, UAB\n   Registrar IANA ID: 1636\n   Registrar Abuse Contact Email: <EMAIL>\n   Registrar Abuse Contact Phone: +37064503378\n   Domain Status: clientTransferProhibited https://icann.org/epp#clientTransferProhibited\n   Name Server: NS1.DNS-PARKING.COM\n   Name Server: NS2.DNS-PARKING.COM\n   DNSSEC: unsigned\n   URL of the ICANN Whois Inaccuracy Complaint Form: https://www.icann.org/wicf/\n>>> Last update of whois database: 2025-05-30T06:09:28Z <<<\n\nFor more information on Whois status codes, please visit https://icann.org/epp\n\nNOTICE: The expiration date displayed in this record is the date the\nregistrar's sponsorship of the domain name registration in the registry is\ncurrently set to expire. This date does not necessarily reflect the expiration\ndate of the domain name registrant's agreement with the sponsoring\nregistrar.  Users may consult the sponsoring registrar's Whois database to\nview the registrar's reported date of expiration for this registration.\n\nTERMS OF USE: You are not authorized to access or query our Whois\ndatabase through the use of electronic processes that are high-volume and\nautomated except as reasonably necessary to register domain names or\nmodify existing registrations; the Data in VeriSign Global Registry\nServices' (\"VeriSign\") Whois database is provided by VeriSign for\ninformation purposes only, and to assist persons in obtaining information\nabout or related to a domain name registration record. VeriSign does not\nguarantee its accuracy. By submitting a Whois query, you agree to abide\nby the following terms of use: You agree that you may use this Data only\nfor lawful purposes and that under no circumstances will you use this Data\nto: (1) allow, enable, or otherwise support the transmission of mass\nunsolicited, commercial advertising or solicitations via e-mail, telephone,\nor facsimile; or (2) enable high volume, automated, electronic processes\nthat apply to VeriSign (or its computer systems). The compilation,\nrepackaging, dissemination or other use of this Data is expressly\nprohibited without the prior written consent of VeriSign. You agree not to\nuse electronic processes that are automated and high-volume to access or\nquery the Whois database except as reasonably necessary to register\ndomain names or modify existing registrations. VeriSign reserves the right\nto restrict your access to the Whois database in its sole discretion to ensure\noperational stability.  VeriSign may restrict or terminate your access to the\nWhois database for failure to abide by these terms of use. VeriSign\nreserves the right to modify these terms at any time.\n\nThe Registry database contains ONLY .COM, .NET, .EDU domains and\nRegistrars.\nClosing connections because of Timeout", "errors": "", "return_code": 0, "progress": "Completado - completed"}, "dig": {"status": "completed", "output": "DNS queries completed", "errors": ""}, "theharvester": {"status": "error", "progress": "theHarvester no está instalado", "errors": "theHarvester no encontrado en el sistema", "output": "Herramienta no disponible en este entorno"}, "assetfinder": {"status": "completed", "output": "support.karedesk.com\nkaredesk.com\nit.karedesk.com\nwww.karedesk.com\nkaredesk.com\nkaredesk.com\nsoporte.karedesk.com\nkaredesk.com\nwww.karedesk.com", "errors": "", "return_code": 0, "progress": "Completado - completed"}, "nuclei": {"status": "timeout", "output": "Aná<PERSON>is parcial por timeout", "errors": "", "return_code": 124, "progress": "Timeout - an<PERSON><PERSON><PERSON> parcial"}, "dig_A": {"status": "completed", "output": "84.32.84.32", "errors": "", "return_code": 0, "progress": "Completado - completed"}, "dig_MX": {"status": "completed", "output": "", "errors": "", "return_code": 0, "progress": "Completado - completed"}, "dig_NS": {"status": "completed", "output": "ns1.dns-parking.com.\nns2.dns-parking.com.", "errors": "", "return_code": 0, "progress": "Completado - completed"}, "dig_summary": {"status": "completed", "output": {"A": ["84.32.84.32"], "MX": [], "NS": ["ns1.dns-parking.com.", "ns2.dns-parking.com."]}}, "theharvester_check": {"status": "error", "output": "", "errors": "", "return_code": 1, "progress": "Completado - error"}, "nuclei_check": {"status": "completed", "output": "/home/<USER>/go/bin/nuclei", "errors": "", "return_code": 0, "progress": "Completado - completed"}, "nuclei_version": {"status": "completed", "output": "", "errors": "[\u001b[34mINF\u001b[0m] Nuclei Engine Version: v3.4.4\n[\u001b[34mINF\u001b[0m] Nuclei Config Directory: /home/<USER>/.config/nuclei\n[\u001b[34mINF\u001b[0m] Nuclei Cache Directory: /home/<USER>/.cache/nuclei\n[\u001b[34mINF\u001b[0m] PDCP Directory: /home/<USER>/.pdcp", "return_code": 0, "progress": "Completado - completed"}, "nuclei_templates": {"status": "completed", "output": "http/cves/2021/CVE-2021-22205.yaml\nhttp/cves/2015/CVE-2015-1880.yaml\nhttp/cves/2024/CVE-2024-33113.yaml\nhttp/cves/2013/CVE-2013-2251.yaml\nhttp/cves/2020/CVE-2020-26214.yaml", "errors": "__     _\n   ____  __  _______/ /__  (_)\n  / __ \\/ / / / ___/ / _ \\/ /\n / / / / /_/ / /__/ /  __/ /\n/_/ /_/\\__,_/\\___/_/\\___/_/   v3.4.4\n\n\t\tprojectdiscovery.io\n\n\nListing available v10.2.2 nuclei templates for /home/<USER>/nuclei-templates", "return_code": 0, "progress": "Completado - completed"}}, "vulnerabilities": [], "recommendations": [{"type": "info", "message": "Implementar un WAF (Web Application Firewall) si no está presente."}, {"type": "info", "message": "Mantener todas las aplicaciones y dependencias actualizadas."}, {"type": "info", "message": "Realizar análisis de seguridad periódicos."}], "summary": {"total_tools": 5, "completed_tools": 3, "failed_tools": 2, "vulnerabilities_found": 0}}