/* Security Scanner Pro - Enhanced CSS */

/* CSS Variables for Theme Support */
:root {
    /* Primary Colors */
    --primary-color: #4f46e5;
    --primary-rgb: 79, 70, 229;
    --secondary-color: #6366f1;
    --accent-color: #8b5cf6;

    /* Status Colors */
    --danger-color: #ef4444;
    --danger-rgb: 239, 68, 68;
    --success-color: #10b981;
    --success-rgb: 16, 185, 129;
    --warning-color: #f59e0b;
    --warning-rgb: 245, 158, 11;
    --info-color: #06b6d4;
    --info-rgb: 6, 182, 212;

    /* Neutral Colors */
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius */
    --border-radius-sm: 0.375rem;
    --border-radius: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
}

/* Dark theme variables */
[data-bs-theme="dark"] {
    --light-color: #1f2937;
    --dark-color: #f8fafc;
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;

    /* Dark theme specific colors */
    --bs-body-bg: #0f172a;
    --bs-body-color: #e2e8f0;
    --bs-border-color: #334155;
    --bs-link-color: #60a5fa;
    --bs-link-hover-color: #93c5fd;
}

/* Dark theme body and background */
[data-bs-theme="dark"] body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: #e2e8f0;
}

/* Dark theme cards */
[data-bs-theme="dark"] .card {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid #334155;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .card-header {
    background: rgba(51, 65, 85, 0.8);
    border-bottom: 1px solid #475569;
    color: #f1f5f9;
}

/* Dark theme navbar */
[data-bs-theme="dark"] .navbar {
    background: rgba(15, 23, 42, 0.95) !important;
    border-bottom: 1px solid #334155;
}

/* Dark theme forms */
[data-bs-theme="dark"] .form-control {
    background-color: #1e293b;
    border-color: #475569;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .form-control:focus {
    background-color: #1e293b;
    border-color: #60a5fa;
    color: #e2e8f0;
    box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25);
}

/* Dark theme buttons */
[data-bs-theme="dark"] .btn-outline-secondary {
    color: #94a3b8;
    border-color: #475569;
}

[data-bs-theme="dark"] .btn-outline-secondary:hover {
    background-color: #475569;
    border-color: #64748b;
    color: #f1f5f9;
}

/* Dark theme tool cards */
[data-bs-theme="dark"] .tool-card {
    background: #1e293b;
    border-color: #475569;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .tool-card:hover {
    background: #334155;
    border-color: #60a5fa;
}

/* Dark theme alerts */
[data-bs-theme="dark"] .alert {
    border: 1px solid;
}

[data-bs-theme="dark"] .alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: #3b82f6;
    color: #93c5fd;
}

[data-bs-theme="dark"] .alert-success {
    background-color: rgba(34, 197, 94, 0.1);
    border-color: #22c55e;
    color: #86efac;
}

[data-bs-theme="dark"] .alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: #f59e0b;
    color: #fbbf24;
}

[data-bs-theme="dark"] .alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: #ef4444;
    color: #fca5a5;
}

/* Dark theme progress bars */
[data-bs-theme="dark"] .progress {
    background-color: #334155;
}

/* Dark theme text colors */
[data-bs-theme="dark"] .text-muted {
    color: #94a3b8 !important;
}

[data-bs-theme="dark"] .text-dark {
    color: #e2e8f0 !important;
}

/* Dark theme borders */
[data-bs-theme="dark"] .border {
    border-color: #475569 !important;
}

/* Dark theme scan progress card */
[data-bs-theme="dark"] .scan-progress-card {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
    border: 1px solid rgba(79, 70, 229, 0.2);
}

/* Dark theme tool status items */
[data-bs-theme="dark"] .tool-status-item {
    background: #1e293b;
    border-color: #475569;
    color: #e2e8f0;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    font-family: var(--font-family-sans);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    min-height: 100vh;
    transition: all var(--transition-normal);
}

/* Enhanced Navigation */
.navbar {
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    transition: all var(--transition-normal);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.brand-text {
    background: linear-gradient(45deg, #fff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.status-indicator {
    display: flex;
    align-items: center;
    font-size: var(--font-size-sm);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    border-bottom: none;
    font-weight: 600;
    padding: 1.25rem 1.5rem;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%);
}

/* Enhanced Tools Grid */
.tools-grid {
    display: grid;
    gap: 0.75rem;
}

.tool-card {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.tool-card:hover {
    background: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.tool-icon-wrapper {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.tool-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.tool-name {
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--gray-800);
    line-height: 1.2;
}

.tool-version {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    line-height: 1;
}

/* Scan Progress Enhancements */
.scan-progress-card {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
    border: 1px solid rgba(79, 70, 229, 0.1);
    border-radius: var(--border-radius-lg);
    padding: 1.25rem;
}

.tool-status-grid {
    margin-top: 1rem;
}

.tool-status-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: white;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    font-size: var(--font-size-xs);
    font-weight: 500;
    position: relative;
    transition: all var(--transition-fast);
}

.tool-status-item i {
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
}

.tool-status-item .status-indicator {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: var(--gray-300);
    transition: all var(--transition-fast);
}

.tool-status-item[data-status="running"] .status-indicator {
    background: var(--warning-color);
    animation: pulse 1.5s infinite;
}

.tool-status-item[data-status="completed"] .status-indicator {
    background: var(--success-color);
}

.tool-status-item[data-status="error"] .status-indicator {
    background: var(--danger-color);
}

/* Scan Button Enhancements */
.scan-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    border-radius: var(--border-radius-lg);
    padding: 0.875rem 1.5rem;
    font-weight: 700;
    letter-spacing: 0.025em;
    transition: all var(--transition-normal);
}

.scan-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.scan-btn:active {
    transform: translateY(0);
}

.scan-btn .btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
}

.scan-btn:disabled {
    opacity: 0.8;
    cursor: not-allowed;
    transform: none;
}

/* Iconos de herramientas */
.tool-icon {
    padding: 10px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.tool-icon:hover {
    background-color: var(--light-color);
    transform: scale(1.05);
}

.tool-icon i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

/* Formularios */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Botones */
.btn {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

/* Barra de progreso */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    border-radius: 10px;
}

/* Alertas */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.1);
}

.alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(39, 174, 96, 0.1);
}

.alert-warning {
    border-left-color: var(--warning-color);
    background-color: rgba(243, 156, 18, 0.1);
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
}

/* Badges de severidad */
.severity-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.severity-critical {
    background-color: #dc3545;
    color: white;
}

.severity-high {
    background-color: #fd7e14;
    color: white;
}

.severity-medium {
    background-color: #ffc107;
    color: #212529;
}

.severity-low {
    background-color: #28a745;
    color: white;
}

/* Características de la página principal */
.feature-item {
    text-align: center;
    padding: 20px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background-color: white;
    box-shadow: var(--shadow);
    transform: translateY(-3px);
}

/* Spinner personalizado */
.custom-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Lista de vulnerabilidades */
.vulnerability-item {
    border-left: 4px solid;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    background-color: white;
    transition: all 0.3s ease;
}

.vulnerability-item:hover {
    box-shadow: var(--shadow);
    transform: translateX(3px);
}

.vulnerability-critical {
    border-left-color: var(--danger-color);
}

.vulnerability-high {
    border-left-color: var(--warning-color);
}

.vulnerability-medium {
    border-left-color: var(--info-color);
}

.vulnerability-low {
    border-left-color: var(--success-color);
}

/* Pre y código */
pre {
    background-color: #2d3748;
    color: #e2e8f0;
    border-radius: var(--border-radius);
    padding: 20px;
    font-size: 0.875rem;
    line-height: 1.5;
    overflow-x: auto;
    border: 1px solid #4a5568;
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}

/* Enhanced Animations */
.fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in {
    animation: slideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
    animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-in {
    animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Scanning Animation */
.scanning-animation {
    position: relative;
}

.scanning-animation::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    margin: -50px 0 0 -50px;
    border: 2px solid transparent;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: scan-rotate 2s linear infinite;
    opacity: 0.3;
}

@keyframes scan-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Vulnerability Badge Animation */
.vulnerability-badge .badge {
    animation: badge-pulse 2s ease-in-out infinite;
}

@keyframes badge-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Progress Bar Enhancements */
.progress {
    background: rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius);
    transition: width 0.6s ease;
}

/* Enhanced Form Controls */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid var(--gray-200);
    transition: all var(--transition-normal);
    font-size: var(--font-size-base);
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    outline: none;
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(var(--success-rgb), 0.1);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(var(--danger-rgb), 0.1);
}

.input-group-text {
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-right: none;
    color: var(--gray-500);
}

/* Enhanced Buttons */
.btn {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 600;
    transition: all var(--transition-normal);
    padding: 0.625rem 1.25rem;
    font-size: var(--font-size-sm);
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%);
    color: white;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Estados de loading */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    z-index: 10;
}

/* Tabla responsive */
.table-responsive {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
}

/* Footer */
footer {
    margin-top: 50px;
    background: linear-gradient(135deg, var(--dark-color), #34495e);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .sticky-top {
        position: relative !important;
        top: auto !important;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .card {
        margin-bottom: 1.5rem;
    }

    .card-body {
        padding: 1rem;
    }

    .navbar-brand {
        font-size: var(--font-size-lg);
    }

    .brand-text {
        display: none;
    }

    .tool-card {
        padding: 0.5rem;
    }

    .tool-icon-wrapper {
        width: 2rem;
        height: 2rem;
        margin-right: 0.5rem;
    }

    .tool-name {
        font-size: var(--font-size-xs);
    }

    .scan-btn {
        padding: 0.75rem 1rem;
        font-size: var(--font-size-sm);
    }

    .tool-status-item {
        padding: 0.375rem 0.5rem;
        font-size: 0.625rem;
    }

    .scanning-animation::before {
        width: 60px;
        height: 60px;
        margin: -30px 0 0 -30px;
    }
}

@media (max-width: 576px) {
    .card-header {
        padding: 1rem;
    }

    .card-body {
        padding: 0.75rem;
    }

    .btn-lg {
        padding: 0.75rem 1rem;
        font-size: var(--font-size-base);
    }

    .tools-grid {
        gap: 0.5rem;
    }

    .tool-status-grid .col-12 .tool-status-item {
        margin-top: 0.5rem;
    }
}

/* Dark Theme Enhancements */
[data-bs-theme="dark"] {
    color-scheme: dark;
}

[data-bs-theme="dark"] body {
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    color: var(--gray-100);
}

[data-bs-theme="dark"] .card {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid var(--gray-700);
}

[data-bs-theme="dark"] .tool-card {
    background: var(--gray-800);
    border-color: var(--gray-600);
    color: var(--gray-200);
}

[data-bs-theme="dark"] .tool-card:hover {
    background: var(--gray-700);
    border-color: var(--primary-color);
}

[data-bs-theme="dark"] .form-control {
    background: var(--gray-800);
    border-color: var(--gray-600);
    color: var(--gray-100);
}

[data-bs-theme="dark"] .form-control:focus {
    background: var(--gray-700);
    border-color: var(--primary-color);
}

[data-bs-theme="dark"] .input-group-text {
    background: var(--gray-700);
    border-color: var(--gray-600);
    color: var(--gray-300);
}

[data-bs-theme="dark"] .scan-progress-card {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
    border-color: rgba(79, 70, 229, 0.2);
}

[data-bs-theme="dark"] .tool-status-item {
    background: var(--gray-800);
    border-color: var(--gray-600);
    color: var(--gray-200);
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .pulse,
    .scanning-animation::before,
    .progress-bar-animated {
        animation: none !important;
    }
}

/* Focus Indicators */
.btn:focus-visible,
.form-control:focus-visible,
.tool-card:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid var(--gray-400);
    }

    .btn {
        border: 2px solid currentColor;
    }

    .tool-card {
        border-width: 2px;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .sticky-top,
    .btn,
    .tool-status-grid {
        display: none !important;
    }

    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* Additional Animations */
@keyframes celebration {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Notification Toast Styles */
.notification-toast {
    position: relative;
    margin-bottom: 0.5rem;
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
}

.notification-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1060;
    max-width: 400px;
}

/* Enhanced Vulnerability Styles */
.vulnerability-item {
    padding: 1rem;
    border-radius: var(--border-radius);
    border-left: 4px solid;
    margin-bottom: 0.75rem;
    background: var(--gray-50);
    transition: all var(--transition-normal);
}

.vulnerability-item:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow);
}

.vulnerability-critical {
    border-left-color: var(--danger-color);
    background: rgba(var(--danger-rgb), 0.05);
}

.vulnerability-high {
    border-left-color: var(--warning-color);
    background: rgba(var(--warning-rgb), 0.05);
}

.vulnerability-medium {
    border-left-color: var(--info-color);
    background: rgba(var(--info-rgb), 0.05);
}

.vulnerability-low {
    border-left-color: var(--success-color);
    background: rgba(var(--success-rgb), 0.05);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes loading-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Enhanced Scrollbar for Results */
.card-body {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--gray-100);
}

.card-body::-webkit-scrollbar {
    width: 6px;
}

.card-body::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--border-radius);
}

.card-body::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: var(--border-radius);
}

.card-body::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-glow {
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.3);
}

/* Interactive Elements */
.interactive-hover {
    transition: all var(--transition-normal);
}

.interactive-hover:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Status Indicators */
.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-dot.success {
    background: var(--success-color);
}

.status-dot.warning {
    background: var(--warning-color);
    animation: pulse 2s infinite;
}

.status-dot.danger {
    background: var(--danger-color);
}

.status-dot.info {
    background: var(--info-color);
}

/* Enhanced Typography */
.text-shadow {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.font-mono {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* Focus Management */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: var(--border-radius);
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Tooltip personalizado */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    color: white;
    border-radius: var(--border-radius);
}

/* Estados de botones */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Efectos de hover en iconos */
.fas, .far, .fab {
    transition: all 0.3s ease;
}

.card-header .fas:hover,
.btn .fas:hover {
    transform: scale(1.1);
}

/* Estilos para números de línea en código */
.line-numbers {
    counter-reset: linenumber;
}

.line-numbers pre {
    counter-increment: linenumber;
}

.line-numbers pre::before {
    content: counter(linenumber);
    color: #6c757d;
    margin-right: 1rem;
    display: inline-block;
    width: 2rem;
    text-align: right;
}

/* Nuclei Console Styles */
.console-container {
    height: 400px;
    overflow-y: auto;
    background: #1e1e1e;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.console-output {
    padding: 10px;
    color: #d4d4d4;
}

.console-line {
    margin-bottom: 2px;
    word-wrap: break-word;
    display: flex;
    align-items: flex-start;
}

.console-timestamp {
    color: #569cd6;
    margin-right: 8px;
    font-weight: bold;
    min-width: 80px;
    flex-shrink: 0;
}

.console-text {
    flex: 1;
}

/* Console line types */
.console-info .console-text {
    color: #4ec9b0;
}

.console-error .console-text {
    color: #f44747;
}

.console-warning .console-text {
    color: #ffcc02;
}

.console-vulnerability .console-text {
    color: #ff6b6b;
    font-weight: bold;
}

.console-stats .console-text {
    color: #9cdcfe;
}

.console-json .console-text {
    color: #ce9178;
    font-size: 12px;
}

.console-output .console-text {
    color: #d4d4d4;
}

/* Stats styling */
.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4ec9b0;
}

.stat-label {
    font-size: 0.8rem;
    color: #9cdcfe;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Console controls */
.console-controls {
    border-top: 1px solid #333;
}

/* Console scrollbar */
.console-container::-webkit-scrollbar {
    width: 8px;
}

.console-container::-webkit-scrollbar-track {
    background: #2d2d30;
}

.console-container::-webkit-scrollbar-thumb {
    background: #464647;
    border-radius: 4px;
}

.console-container::-webkit-scrollbar-thumb:hover {
    background: #5a5a5c;
}

/* Console animations */
.console-line.new-line {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Console toggle button */
#toggleConsole i {
    transition: transform 0.3s ease;
}

#toggleConsole.collapsed i {
    transform: rotate(180deg);
}

/* Minimized console */
.console-minimized .console-container {
    display: none;
}

.console-minimized .console-controls {
    display: none;
}

/* Responsive console */
@media (max-width: 768px) {
    .console-container {
        height: 300px;
        font-size: 12px;
    }

    .stat-value {
        font-size: 1.2rem;
    }

    .stat-label {
        font-size: 0.7rem;
    }
}

