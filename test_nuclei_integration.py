#!/usr/bin/env python3
"""
Test script for Nuclei integration and dark mode functionality
"""

import requests
import json
import time
import sys
import subprocess

def test_nuclei_command():
    """Test Nuclei command directly"""
    print("🧪 Testing Nuclei command...")
    
    try:
        # Test basic Nuclei functionality
        cmd = [
            "nuclei", "-target", "https://httpbin.org", 
            "-tags", "tech", "-silent", "-timeout", "5", 
            "-c", "1", "-disable-update-check"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        print(f"   Return code: {result.returncode}")
        print(f"   Stdout length: {len(result.stdout)}")
        print(f"   Stderr length: {len(result.stderr)}")
        
        if result.returncode == 0:
            print("   ✅ Nuclei command executed successfully")
            return True
        else:
            print(f"   ⚠️  Nuclei returned code {result.returncode}")
            if result.stderr:
                print(f"   Error: {result.stderr[:200]}")
            return True  # Non-zero exit is normal for no findings
            
    except subprocess.TimeoutExpired:
        print("   ⏰ Nuclei command timed out (normal)")
        return True
    except Exception as e:
        print(f"   ❌ Error running Nuclei: {e}")
        return False

def test_flask_app():
    """Test Flask application endpoints"""
    print("\n🌐 Testing Flask application...")
    
    base_url = "http://localhost:3333"
    
    try:
        # Test main page
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("   ✅ Main page accessible")
        else:
            print(f"   ❌ Main page returned {response.status_code}")
            return False
            
        # Test scan endpoint with a simple domain
        scan_data = {"target": "example.com"}
        response = requests.post(f"{base_url}/scan", json=scan_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            scan_id = data.get('scan_id')
            print(f"   ✅ Scan started successfully: {scan_id[:8]}...")
            
            # Wait a bit and check status
            time.sleep(3)
            status_response = requests.get(f"{base_url}/status/{scan_id}", timeout=5)
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"   ✅ Status endpoint working: {status_data.get('status', 'unknown')}")
                return True
            else:
                print(f"   ❌ Status endpoint returned {status_response.status_code}")
                return False
        else:
            print(f"   ❌ Scan endpoint returned {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to Flask app. Is it running on localhost:3333?")
        return False
    except Exception as e:
        print(f"   ❌ Error testing Flask app: {e}")
        return False

def test_static_files():
    """Test that static files are loading"""
    print("\n📁 Testing static files...")
    
    base_url = "http://localhost:3333"
    static_files = [
        "/static/vendor/bootstrap/bootstrap.min.css",
        "/static/vendor/fontawesome/all.min.css",
        "/static/vendor/bootstrap/bootstrap.bundle.min.js",
        "/static/css/custom.css",
        "/static/js/app.js"
    ]
    
    try:
        for file_path in static_files:
            response = requests.get(f"{base_url}{file_path}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {file_path}")
            else:
                print(f"   ❌ {file_path} - Status: {response.status_code}")
                return False
        
        print("   ✅ All static files loading correctly")
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing static files: {e}")
        return False

def test_dark_mode_css():
    """Test dark mode CSS is present"""
    print("\n🌙 Testing dark mode CSS...")
    
    try:
        with open('static/css/custom.css', 'r') as f:
            css_content = f.read()
        
        dark_mode_indicators = [
            '[data-bs-theme="dark"]',
            '--bs-body-bg: #0f172a',
            'background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
            'Dark theme variables'
        ]
        
        for indicator in dark_mode_indicators:
            if indicator in css_content:
                print(f"   ✅ Found: {indicator}")
            else:
                print(f"   ❌ Missing: {indicator}")
                return False
        
        print("   ✅ Dark mode CSS properly implemented")
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing dark mode CSS: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 Security Scanner - Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Nuclei Command", test_nuclei_command),
        ("Flask Application", test_flask_app),
        ("Static Files", test_static_files),
        ("Dark Mode CSS", test_dark_mode_css)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fixes are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
